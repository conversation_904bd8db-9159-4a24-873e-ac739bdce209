import React, { useEffect, useState } from "react";
import { Dropdown } from "primereact/dropdown";
import { But<PERSON> } from "primereact/button";

export default function CardAssignmentCell({
  rowData,
  getAvailableCards,
  checkCardAssignmentStatus,
  pendingCardAssignments,
  memberCards,
  handleAssignCard,
  handleUnassignCard,
}) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [assignmentStatus, setAssignmentStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let mounted = true;
    const fetchAssignmentStatus = async () => {
      setIsLoading(true);
      try {
        const status = await checkCardAssignmentStatus(rowData.id);
        if (!mounted) return;
        setAssignmentStatus(status);
      } finally {
        if (mounted) setIsLoading(false);
      }
    };

    fetchAssignmentStatus();
    return () => { mounted = false; };
  }, [rowData.id, memberCards, pendingCardAssignments, checkCardAssignmentStatus]);

  const availableCards = getAvailableCards(rowData.id);

  const pendingAssignment = pendingCardAssignments?.[rowData.id];
  const hasPendingAssignment = pendingAssignment && pendingAssignment.action === "assign";
  const hasPendingUnassignment = pendingAssignment && pendingAssignment.action === "unassign";

  if (isLoading) {
    return (
      <div className="flex justify-center">
        <i className="pi pi-spin pi-spinner" />
      </div>
    );
  }

  if (hasPendingAssignment) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3 w-[200px]">
        <div className="flex justify-between items-center mb-2">
          <span className="text-xs font-semibold text-yellow-600 dark:text-yellow-400">PENDING ASSIGNMENT</span>
          <button
            onClick={() => handleUnassignCard(rowData.id, pendingAssignment.cardNumber)}
            className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
            title="Cancel Assignment"
          >
            <i className="pi pi-times"></i>
          </button>
        </div>
        <div className="font-mono text-sm font-bold tracking-wider text-yellow-800 dark:text-yellow-300">
          {pendingAssignment.cardNumber}
        </div>
      </div>
    );
  }

  if (hasPendingUnassignment) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-3 w-[200px]">
        <div className="flex justify-between items-center mb-2">
          <span className="text-xs font-semibold text-red-600 dark:text-red-400">PENDING UNASSIGNMENT</span>
          <button
            onClick={() => handleUnassignCard(rowData.id, pendingAssignment.cardNumber)}
            className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
            title="Cancel Unassignment"
          >
            <i className="pi pi-times"></i>
          </button>
        </div>
        <div className="font-mono text-sm font-bold tracking-wider text-red-800 dark:text-red-300">
          {pendingAssignment.cardNumber}
        </div>
      </div>
    );
  }

  if (assignmentStatus?.status === "assigned") {
    return (
      <Button
        severity="danger"
        label="Unassign Card"
        tooltip="Unassign Card"
        tooltipOptions={{ position: "top" }}
        onClick={() => handleUnassignCard(rowData.id, assignmentStatus.card?.number)}
        disabled={isUpdating}
        className="w-[200px]"
        style={{
          backgroundColor: "#ef4444",
          borderColor: "#ef4444",
          color: "white",
          width: "200px",
          borderRadius: "8px",
        }}
        pt={{
          root: {
            className: "!w-[200px] !rounded-lg dark:!bg-red-600 dark:!border-red-600",
          },
          label: {
            className: "!text-white",
          },
        }}
      />
    );
  }

  if (availableCards?.length > 0) {
    return (
      <div className="flex items-center gap-2">
        <div className="relative w-[200px]">
          <Dropdown
            value={null}
            options={availableCards}
            onChange={(e) => e.value && handleAssignCard(rowData.id, e.value)}
            optionLabel="label"
            className="w-full"
            panelClassName="border border-gray-200 dark:border-gray-600 shadow-xl rounded-lg mt-1"
            dropdownIcon="pi pi-chevron-down"
            placeholder={`Assign Card (${availableCards.length} available)`}
            pt={{
              root: {
                className:
                  "border border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-colors",
              },
              trigger: { className: "bg-white dark:bg-gray-800" },
              panel: { className: "shadow-lg bg-white dark:bg-gray-800" },
              item: ({ context }) => ({
                className: context.selected
                  ? "bg-blue-50 dark:bg-blue-900/20"
                  : "hover:bg-gray-50 dark:hover:bg-gray-700",
              }),
            }}
            itemTemplate={(option) => (
              <div className="flex items-center justify-between p-2">
                <div className="flex items-center">
                  {option.cardData?.card_type?.type_of_connection === "NFC" ? (
                    <div className="text-blue-500 dark:text-blue-400 mr-3">
                      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M20 4L20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        <path d="M20 4L16 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                      </svg>
                    </div>
                  ) : (
                    <div className="text-purple-500 dark:text-purple-400 mr-3">
                      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  )}
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">{option.label}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {option.cardData?.card_type?.type_of_connection || "Unknown"} Card
                    </div>
                  </div>
                </div>
                {option.number && (
                  <div className="font-mono text-xs bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-2 py-1 rounded">
                    {option.number.slice(-4)}
                  </div>
                )}
              </div>
            )}
          />
          {isUpdating && (
            <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center">
              <i className="pi pi-spinner pi-spin text-blue-500"></i>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="text-center p-3 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800 w-[200px]">
      <i className="pi pi-info-circle text-gray-400 dark:text-gray-500 mb-1"></i>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        {Object.keys(pendingCardAssignments || {}).length > 0
          ? "No cards available (some may be pending assignment)"
          : "No available cards"}
      </p>
    </div>
  );
}

