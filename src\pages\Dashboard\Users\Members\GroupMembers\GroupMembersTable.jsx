import React from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import CardAssignmentCell from "./CardAssignmentCell";

export default function GroupMembersTable({
  data,
  selectedUnassignUsers,
  setSelectedUnassignUsers,
  lazyParams,
  totalRecords,
  loading,
  dataHandler,
  imageBodyTemplate,
  statusBodyTemplate,
  actionBodyTemplate,
  getAvailableCards,
  checkCardAssignmentStatus,
  pendingCardAssignments,
  memberCards,
  handleAssignCard,
  handleUnassignCard,
}) {
  return (
    <DataTable
      selection={selectedUnassignUsers}
      onSelectionChange={(e) => setSelectedUnassignUsers(e.value)}
      lazy
      responsiveLayout="stack"
      breakpoint="960px"
      dataKey="id"
      paginator
      className="border-t-0 dark:bg-gray-800"
      rowClassName="row dark:bg-slate-800 dark:hover:bg-slate-700/50"
      value={data}
      first={lazyParams?.first}
      rows={lazyParams?.rows}
      rowsPerPageOptions={[5, 25, 50, 100]}
      totalRecords={totalRecords}
      onPage={dataHandler}
      onSort={dataHandler}
      sortField={lazyParams?.sortField}
      sortOrder={lazyParams?.sortOrder}
      onFilter={dataHandler}
      filters={lazyParams?.filters}
      loading={loading}
      scrollable
      scrollHeight="100%"
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
      pt={{
        header: { className: 'dark:bg-gray-800 dark:border-gray-700' },
        headerCell: { className: 'dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700' },
        columnHeader: { className: 'dark:bg-gray-800 dark:text-gray-200' },
        headerCheckbox: { className: 'dark:bg-gray-800' },
        table: { className: 'dark:bg-gray-800' },
        tbody: { className: 'dark:bg-gray-800' },
        row: { className: 'dark:bg-gray-800 dark:hover:bg-gray-700' },
        cell: { className: 'dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700' },
        thead: { className: 'dark:bg-gray-800' },
        headerRow: { className: 'dark:bg-gray-800 dark:border-gray-700' }
      }}
      header={
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={data && data.length > 0 && selectedUnassignUsers.length === data.length}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedUnassignUsers(data || []);
                } else {
                  setSelectedUnassignUsers([]);
                }
              }}
              className="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
            />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Select All ({selectedUnassignUsers.length}/{data?.length || 0} selected)
            </span>
          </div>
          {selectedUnassignUsers.length > 0 && (
            <button
              onClick={() => setSelectedUnassignUsers([])}
              className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
            >
              Clear Selection
            </button>
          )}
        </div>
      }
    >
      <Column selectionMode="multiple" headerStyle={{ width: '3em' }} />
      <Column body={imageBodyTemplate} header="Template" className="text-center" style={{ width: '275px', minWidth: '250px' }} />
      <Column field="name" header="Name" sortable style={{ minWidth: '200px', width: '200px', maxWidth: '250px' }} />
      <Column field="type" header="Type" sortable style={{ minWidth: '130px', width: '130px' }} />
      <Column field="position" header="Position" sortable style={{ minWidth: '130px', width: '130px' }} />
      <Column field="department" header="Department" sortable style={{ minWidth: '130px', width: '130px' }} />
      <Column header="Card Details" body={(rowData) => {
        const card = (rowData.cards && rowData.cards.length > 0) ? rowData.cards[0] : null;
        return (
          <div className="flex flex-col items-center gap-1">
            {card ? (
              <div className="text-sm font-mono font-semibold bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                {card.number || 'N/A'}
              </div>
            ) : (
              <div className="text-sm text-gray-500 dark:text-gray-400 italic">No card assigned</div>
            )}
          </div>
        );
      }} style={{ minWidth: '260px', width: '260px' }} />
      <Column
        header="Card Assignment"
        body={(rowData) => (
          <CardAssignmentCell
            rowData={rowData}
            getAvailableCards={getAvailableCards}
            checkCardAssignmentStatus={checkCardAssignmentStatus}
            pendingCardAssignments={pendingCardAssignments}
            memberCards={memberCards}
            handleAssignCard={handleAssignCard}
            handleUnassignCard={handleUnassignCard}
          />
        )}
        style={{ minWidth: '260px', width: '260px' }}
      />
      <Column field="status" body={statusBodyTemplate} header="Status" className="text-center" style={{ minWidth: '220px', width: '220px' }} />
      <Column body={actionBodyTemplate} header="Actions" exportable={false} style={{ minWidth: '200px', maxWidth: '250px', width: '220px' }} />
    </DataTable>
  );
}

