import React, { useEffect, useState } from "react";
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { FaUserTie, FaBuilding, FaIdCard, FaUserTag } from 'react-icons/fa';
import { HiBriefcase } from 'react-icons/hi';

// Extracted member card from GroupMembersPage.jsx
export default function GroupMemberCard({
  member,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  onTemplateView,
  lazyParams,
  statusStyles,
  profile_img,
  handleAssignCard,
  handleUnassignCard,
  checkCardAssignmentStatus,
  getAvailableCards,
  pendingCardAssignments,
  memberCards,
  setSelectedMember,
  dialogHandler,
  handleDeleteMemberClick,
  confirmDialog,
  setSelectedTemplate,
  setTemplateModalVisible,
  groupId,
}) {
  const [cardDetails, setCardDetails] = useState(null);
  const [cardDetailsLoading, setCardDetailsLoading] = useState(true);
  const [assignmentStatus, setAssignmentStatus] = useState(null);
  const [assignmentLoading, setAssignmentLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const fetchCardDetails = async () => {
      setCardDetailsLoading(true);
      const status = await checkCardAssignmentStatus(member.id);
      if (status && status.status === 'assigned') {
        setCardDetails(status.card);
      } else {
        setCardDetails(null);
      }
      setCardDetailsLoading(false);
    };

    fetchCardDetails();
  }, [member.id, pendingCardAssignments]);

  useEffect(() => {
    const fetchAssignmentStatus = async () => {
      setAssignmentLoading(true);
      const status = await checkCardAssignmentStatus(member.id);
      setAssignmentStatus(status);
      setAssignmentLoading(false);
    };

    fetchAssignmentStatus();
  }, [member.id, memberCards, pendingCardAssignments]);

  const availableCards = getAvailableCards(member.id);
  const pendingAssignment = pendingCardAssignments[member.id];
  const hasPendingAssignment = pendingAssignment && pendingAssignment.action === 'assign';
  const hasPendingUnassignment = pendingAssignment && pendingAssignment.action === 'unassign';

  return (
    <div className={`group relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border-2 overflow-hidden ${
      isSelected ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' : 'border-gray-200 dark:border-gray-700'
    }`}>
      {/* Checkbox Selection - top-right */}
      <div className="absolute top-3 right-3 z-10">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onSelect}
          className="w-5 h-5 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
        />
      </div>

      {/* Member Profile Header */}
      <div className="relative px-4 pt-4 pb-3 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-gray-800 border-b border-gray-200 dark:border-gray-600">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img
              src={member.image || profile_img}
              alt={member.name}
              className="w-12 h-12 rounded-full border-3 border-white dark:border-gray-800 shadow-lg object-cover"
            />
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full border-2 border-white dark:border-gray-800 shadow-md"></div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white truncate flex items-center" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              {member.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 truncate flex items-center font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              <HiBriefcase className="w-3 h-3 mr-1 text-gray-500 dark:text-gray-400" />
              {member.position || 'Position'}
            </p>
          </div>
          {/* Status Indicator */}
          <div className="relative">
            {cardDetails ? (
              <>
                <div className="w-3 h-3 bg-emerald-400 rounded-full shadow-lg animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-emerald-400 rounded-full animate-ping opacity-75"></div>
              </>
            ) : (
              <>
                <div className="w-3 h-3 bg-red-400 rounded-full shadow-lg animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-red-400 rounded-full animate-ping opacity-75"></div>
              </>
            )}
          </div>
        </div>

        {/* Member Type and Department */}
        <div className="mt-2 flex flex-wrap gap-1">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 dark:from-purple-900/30 dark:to-pink-900/30 dark:text-purple-300 shadow-sm border border-purple-200/50 dark:border-purple-700/50" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
            <FaUserTag className="w-3 h-3 mr-1 text-purple-600 dark:text-purple-400" />
            {member.type || 'Member'}
          </span>
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 dark:from-blue-900/30 dark:to-indigo-900/30 dark:text-blue-300 shadow-sm border border-blue-200/50 dark:border-blue-700/50" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
            <FaBuilding className="w-3 h-3 mr-1 text-blue-600 dark:text-blue-400" />
            {member.department || 'Department'}
          </span>
        </div>
      </div>

      {/* Card Status Section */}
      <div className={`relative px-4 py-3 ${
        cardDetails
          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20'
          : 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20'
      }`}>
        {cardDetailsLoading ? (
          <div className="flex justify-center py-2">
            <i className="pi pi-spin pi-spinner text-blue-500"></i>
          </div>
        ) : cardDetails ? (
          <div className="text-center">
            <div className="text-xs font-semibold mb-1 tracking-wider flex items-center justify-center text-blue-600 dark:text-blue-400">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
              CARD NUMBER
            </div>
            <div className="font-mono text-lg font-bold tracking-widest text-blue-800 dark:text-blue-200">
              {cardDetails.number || 'N/A'}
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-xs font-semibold mb-1 tracking-wider flex items-center justify-center text-red-600 dark:text-red-400">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
              STATUS
            </div>
            <div className="text-sm font-semibold text-red-700 dark:text-red-300">
              No Card Assigned
            </div>
          </div>
        )}
      </div>

      {/* Main Content Section */}
      <div className="relative px-4 pb-4">
        {/* Card Design/Number Display - compact */}
        <div className="space-y-2 mb-3">
          <div className="bg-gradient-to-r from-blue-50 to-gray-50 dark:from-blue-900/20 dark:to-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-2 shadow-sm">
            <div className="flex justify-between items-center mb-1">
              <span className="text-xs font-semibold text-blue-600 dark:text-blue-400 flex items-center">
                <FaIdCard className="w-3 h-3 mr-1" />
                {member.template_image_html ? 'CARD DESIGN' : 'CARD NUMBER'}
              </span>
            </div>
            {cardDetailsLoading ? (
              <div className="flex justify-center py-1">
                <i className="pi pi-spin pi-spinner text-blue-500 text-sm"></i>
              </div>
            ) : cardDetails ? (
              member.template_image_html ? (
                <div className="relative bg-gradient-to-br from-white via-gray-50 to-white dark:from-slate-700 dark:via-gray-700 dark:to-slate-700 rounded-xl border border-gray-200 dark:border-slate-500 shadow-lg overflow-hidden" style={{ minHeight: '100px', height: 'auto' }}>
                  <div className="w-full flex items-center justify-center p-1" style={{ background: '#ffffff', borderRadius: '12px', overflow: 'hidden', minHeight: '90px' }}>
                    <div className="w-full flex items-center justify-center" style={{ background: '#ffffff', maxWidth: '100%', overflow: 'hidden', minHeight: '80px' }}>
                      <div
                        style={{ width: '100%', minHeight: '70px', background: '#ffffff', display: 'flex', alignItems: 'center', justifyContent: 'center', position: 'relative' }}
                        dangerouslySetInnerHTML={{
                          __html: (() => {
                            const fullTemplate = member.template_image_html.replace(
                              'https://www.gravatar.com/avatar/?d=mp',
                              member.image || 'https://www.gravatar.com/avatar/?d=mp'
                            );
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = fullTemplate;
                            const captureSection = tempDiv.querySelector('[class*="capture"], [id*="capture"], .photo-section, .image-section, .profile-section, [class*="photo"], [class*="image"], [class*="profile"]');
                            if (captureSection) {
                              const captureRect = captureSection.getBoundingClientRect();
                              const captureWidth = captureSection.offsetWidth || captureRect.width;
                              const captureHeight = captureSection.offsetHeight || captureRect.height;
                              const containerWidth = 110;
                              const containerHeight = 110;
                              const scaleX = containerWidth / captureWidth;
                              const scaleY = containerHeight / captureHeight;
                              const scale = Math.min(scaleX, scaleY, 0.5);
                              captureSection.style.transform = `scale(${scale})`;
                              captureSection.style.transformOrigin = 'center center';
                              captureSection.style.width = '100%';
                              captureSection.style.height = '100%';
                              return captureSection.outerHTML;
                            }
                            const firstImage = tempDiv.querySelector('img');
                            if (firstImage) return firstImage.outerHTML;
                            return fullTemplate;
                          })()
                        }}
                      />
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-0 hover:opacity-100 transition-opacity duration-500 rounded-t-xl"></div>
                  <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="px-2 py-1 bg-gradient-to-r from-slate-600 via-slate-700 to-slate-800 text-white text-xs font-bold rounded-full shadow-lg border border-slate-500"
                         style={{ background: 'linear-gradient(135deg, #475569 0%, #334155 50%, #1e293b 100%)', boxShadow: '0 2px 8px rgba(71, 85, 105, 0.4)', textShadow: '0 1px 2px rgba(0,0,0,0.3)' }}>
                      <div className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-slate-300 rounded-full opacity-80"></div>
                        Design
                        <div className="w-1 h-1 bg-slate-300 rounded-full opacity-80"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex justify-between items-center">
                  <span className="font-mono text-sm font-bold tracking-wider text-gray-900 dark:text-gray-100">
                    {cardDetails.number || 'N/A'}
                  </span>
                  <i className="pi pi-credit-card text-gray-400 dark:text-gray-500 ml-2"></i>
                </div>
              )
            ) : (
              <span className="text-sm text-gray-500 dark:text-gray-400 italic flex items-center">
                <i className="pi pi-info-circle mr-1"></i>
                No card assigned
              </span>
            )}
          </div>

          {/* Card Assignment Section */}
          <div className="p-2 bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-700/50 dark:to-slate-700/50 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm">
            <h4 className="text-xs font-semibold text-gray-700 dark:text-gray-300 mb-1 flex items-center">
              <FaUserTie className="w-3 h-3 mr-1 text-indigo-600 dark:text-indigo-400" />
              Card Assignment
            </h4>
            {assignmentLoading ? (
              <div className="flex justify-center py-1">
                <i className="pi pi-spin pi-spinner text-blue-500 text-sm"></i>
              </div>
            ) : hasPendingAssignment ? (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded p-1">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-semibold text-yellow-600 dark:text-yellow-400">PENDING</span>
                  <button onClick={() => handleUnassignCard(member.id, pendingAssignment.cardNumber)} className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300" title="Cancel Assignment">
                    <i className="pi pi-times text-xs"></i>
                  </button>
                </div>
                <div className="font-mono text-xs font-bold text-yellow-800 dark:text-yellow-300">{pendingAssignment.cardNumber}</div>
              </div>
            ) : hasPendingUnassignment ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded p-1">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-semibold text-red-600 dark:text-red-400">UNASSIGNING</span>
                  <button onClick={() => handleUnassignCard(member.id, pendingAssignment.cardNumber)} className="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300" title="Cancel Unassignment">
                    <i className="pi pi-times text-xs"></i>
                  </button>
                </div>
                <div className="font-mono text-xs font-bold text-red-800 dark:text-red-300">{pendingAssignment.cardNumber}</div>
              </div>
            ) : assignmentStatus?.status !== 'assigned' ? (
              availableCards.length > 0 ? (
                <div className="relative">
                  <Dropdown
                    value={null}
                    options={availableCards}
                    onChange={(e) => e.value && handleAssignCard(member.id, e.value)}
                    optionLabel="label"
                    className="w-full text-xs"
                    panelClassName="border border-gray-200 dark:border-gray-600 shadow-xl rounded-lg mt-1"
                    dropdownIcon="pi pi-chevron-down"
                    placeholder={`Assign (${availableCards.length})`}
                    pt={{
                      root: { className: 'border border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-colors text-xs' },
                      trigger: { className: 'bg-white dark:bg-gray-800 text-xs' },
                      panel: { className: 'shadow-lg bg-white dark:bg-gray-800' },
                      item: ({ context }) => ({
                        className: context.selected ? 'bg-blue-50 dark:bg-blue-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                      }),
                    }}
                    itemTemplate={(option) => (
                      <div className="flex items-center justify-between p-1">
                        <div className="flex items-center">
                          {option.cardData?.card_type?.type_of_connection === 'NFC' ? (
                            <div className="text-blue-500 dark:text-blue-400 mr-2">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              </svg>
                            </div>
                          ) : (
                            <div className="text-purple-500 dark:text-purple-400 mr-2">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 8L18 16L12 22V2L18 8L6 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900 dark:text-gray-100 text-xs">{option.label}</div>
                          </div>
                        </div>
                        {option.number && (
                          <div className="font-mono text-xs bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-1 py-0.5 rounded">
                            {option.number.slice(-4)}
                          </div>
                        )}
                      </div>
                    )}
                  />
                  {isUpdating && (
                    <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center">
                      <i className="pi pi-spinner pi-spin text-blue-500 text-xs"></i>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center p-1 border border-dashed border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800">
                  <p className="text-xs text-gray-600 dark:text-gray-400">No cards available</p>
                </div>
              )
            ) : (
              <Button
                severity="danger"
                label="Unassign"
                tooltip="Unassign Card"
                tooltipOptions={{ position: 'top' }}
                onClick={() => handleUnassignCard(member.id, assignmentStatus.card.number)}
                disabled={isUpdating}
                className="w-full text-xs"
                style={{ backgroundColor: '#ef4444', borderColor: '#ef4444', color: 'white', borderRadius: '6px', fontSize: '11px', padding: '4px 8px' }}
              />
            )}
          </div>

          {/* Print Status */}
          <div className="flex items-center justify-between p-2 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-700 rounded-lg shadow-sm">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 flex items-center">
              <i className="pi pi-print mr-1 text-amber-600 dark:text-amber-400 text-xs"></i>
              Print Status
            </span>
            {!lazyParams?.designID ? (
              <div className="flex items-center gap-1">
                <span className="text-xs font-medium text-amber-700 dark:text-amber-300">Design Required</span>
              </div>
            ) : (
              <span className={`px-1 py-0.5 rounded-full text-xs font-medium text-white shadow-sm ${statusStyles[member.print_status?.toLowerCase() || 'unprinted']}`}>
                {member.print_status || 'Unprinted'}
              </span>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex space-x-1">
            <button onClick={onEdit} className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-all duration-200">
              <FiEdit className="w-3 h-3" />
            </button>
            <button onClick={onDelete} className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-all duration-200">
              <TfiTrash className="w-3 h-3" />
            </button>
          </div>
          {member.template_image_html && (
            <button onClick={onTemplateView} className="px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:hover:bg-indigo-900/50 rounded transition-all duration-200">
              View
            </button>
          )}
        </div>
      </div>

      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
    </div>
  );
}

