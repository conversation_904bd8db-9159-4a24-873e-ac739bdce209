import React from "react";
import GroupMemberCard from "./GroupMemberCard";

export default function GroupMembersCards({
  data,
  selectedUnassignUsers,
  setSelectedUnassignUsers,
  setSelectedMember,
  dialogHandler,
  handleDeleteMemberClick,
  groupID,
  setSelectedTemplate,
  setTemplateModalVisible,
  lazyParams,
  statusStyles,
  profile_img,
  handleAssignCard,
  handleUnassignCard,
  checkCardAssignmentStatus,
  getAvailableCards,
  pendingCardAssignments,
  memberCards,
  dataHandler,
  totalRecords,
}) {
  return (
    <div className="space-y-4">
      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data?.map((member) => (
          <GroupMemberCard
            key={member.id}
            member={member}
            isSelected={selectedUnassignUsers.some((selected) => selected.id === member.id)}
            onSelect={() => {
              const isSelected = selectedUnassignUsers.some((selected) => selected.id === member.id);
              if (isSelected) {
                setSelectedUnassignUsers((prev) => prev.filter((selected) => selected.id !== member.id));
              } else {
                setSelectedUnassignUsers((prev) => [...prev, member]);
              }
            }}
            onEdit={() => {
              setSelectedMember(member);
              dialogHandler("addMember");
            }}
            setSelectedMember={setSelectedMember}
            dialogHandler={dialogHandler}
            handleDeleteMemberClick={handleDeleteMemberClick}
            confirmDialog={null}
            setSelectedTemplate={setSelectedTemplate}
            setTemplateModalVisible={setTemplateModalVisible}
            groupId={groupID}
            onDelete={() => handleDeleteMemberClick(member, groupID)}
            onTemplateView={() => {
              let templateWithUserData = member.template_image_html;
              if (templateWithUserData?.includes('https://www.gravatar.com/avatar/?d=mp')) {
                templateWithUserData = templateWithUserData.replace(
                  'https://www.gravatar.com/avatar/?d=mp',
                  member.image || 'https://www.gravatar.com/avatar/?d=mp'
                );
              }
              setSelectedTemplate(templateWithUserData);
              setTemplateModalVisible(true);
            }}
            lazyParams={lazyParams}
            statusStyles={statusStyles}
            profile_img={profile_img}
            handleAssignCard={handleAssignCard}
            handleUnassignCard={handleUnassignCard}
            checkCardAssignmentStatus={checkCardAssignmentStatus}
            getAvailableCards={getAvailableCards}
            pendingCardAssignments={pendingCardAssignments}
            memberCards={memberCards}
          />
        ))}
      </div>

      {/* Pagination for Cards */}
      <div className="flex justify-center w-full px-4 pb-4">
        <div className="flex items-center gap-2 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 max-w-fit">
          <button
            onClick={() => dataHandler({ first: 0, rows: lazyParams?.rows || 9 })}
            disabled={!lazyParams?.first || lazyParams.first === 0}
            className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            First
          </button>
          <button
            onClick={() => dataHandler({ first: Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 9)), rows: lazyParams?.rows || 9 })}
            disabled={!lazyParams?.first || lazyParams.first === 0}
            className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Previous
          </button>
          <span className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400">
            Page {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 9)) + 1} of {Math.ceil(totalRecords / (lazyParams?.rows || 9))}
          </span>
          <button
            onClick={() => dataHandler({ first: (lazyParams?.first || 0) + (lazyParams?.rows || 9), rows: lazyParams?.rows || 9 })}
            disabled={(lazyParams?.first || 0) + (lazyParams?.rows || 9) >= totalRecords}
            className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Next
          </button>
          <button
            onClick={() => dataHandler({ first: Math.max(0, totalRecords - (lazyParams?.rows || 9)), rows: lazyParams?.rows || 9 })}
            disabled={(lazyParams?.first || 0) + (lazyParams?.rows || 9) >= totalRecords}
            className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Last
          </button>
          <select
            value={lazyParams?.rows || 9}
            onChange={(e) => dataHandler({ first: 0, rows: parseInt(e.target.value) })}
            className="px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-300"
          >
            <option value={9}>9 per page</option>
            <option value={18}>18 per page</option>
            <option value={27}>27 per page</option>
            <option value={36}>36 per page</option>
          </select>
        </div>
      </div>
    </div>
  );
}

