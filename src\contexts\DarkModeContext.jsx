import React, { createContext, useContext, useState, useEffect } from 'react';

const DarkModeContext = createContext();

// Helper function to apply dark mode classes efficiently
const applyDarkModeClasses = (isDark) => {
  const html = document.documentElement;
  const body = document.body;
  
  if (isDark) {
    html.classList.add('dark');
    body.classList.add('dark');
  } else {
    html.classList.remove('dark');
    body.classList.remove('dark');
  }
};

// Helper function to get initial dark mode state
const getInitialDarkMode = () => {
  try {
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode !== null) {
      return JSON.parse(savedMode);
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  } catch (error) {
    console.warn('Error reading dark mode preference:', error);
    return false;
  }
};

export const DarkModeProvider = ({ children }) => {
  const [isDarkMode, setDarkMode] = useState(() => {
    const mode = getInitialDarkMode();
    // Apply immediately on initialization
    applyDarkModeClasses(mode);
    return mode;
  });

  // Handle dark mode changes
  useEffect(() => {
    try {
      localStorage.setItem('darkMode', JSON.stringify(isDarkMode));
      applyDarkModeClasses(isDarkMode);
    } catch (error) {
      console.warn('Error saving dark mode preference:', error);
    }
  }, [isDarkMode]);

  // Toggle function
  const toggleDarkMode = () => {
    setDarkMode(prevMode => !prevMode);
  };

  // Setter function
  const setDarkModeValue = (value) => {
    setDarkMode(value);
  };

  const contextValue = {
    isDarkMode,
    toggleDarkMode,
    setDarkMode: setDarkModeValue
  };

  return (
    <DarkModeContext.Provider value={contextValue}>
      {children}
    </DarkModeContext.Provider>
  );
};

export const useDarkMode = () => {
  const context = useContext(DarkModeContext);
  if (context === undefined) {
    throw new Error('useDarkMode must be used within a DarkModeProvider');
  }
  return context;
};
