.dark .p-datatable .p-datatable-header {
  background-color: transparent !important;
  border-color: rgb(55 65 81) !important;
  color: rgb(229 231 235) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th {
  background-color: transparent !important;
  border-color: rgb(55 65 81) !important;
  color: rgb(229 231 235) !important;
  padding: 0.75rem !important;
}

.dark .p-datatable .p-datatable-thead > tr > th.p-selection-column {
  background-color: transparent !important;
  border-color: rgb(55 65 81) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th.p-sortable-column:hover {
  background-color: rgb(55 65 81) !important;
  color: rgb(243 244 246) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th.p-sortable-column:focus {
  background-color: rgb(55 65 81) !important;
  color: rgb(243 244 246) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th.p-sortable-column.p-highlight {
  background-color: rgb(55 65 81) !important;
  color: rgb(243 244 246) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th .p-column-header-content {
  background-color: transparent !important;
  color: rgb(229 231 235) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th .p-sortable-column-icon {
  color: rgb(229 231 235) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th .p-checkbox {
  background-color: rgb(55 65 81) !important;
  border-color: rgb(75 85 99) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th .p-checkbox .p-checkbox-box {
  background-color: rgb(55 65 81) !important;
  border-color: rgb(75 85 99) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th .p-checkbox .p-checkbox-box.p-highlight {
  background-color: rgb(59 130 246) !important;
  border-color: rgb(59 130 246) !important;
}

/* إصلاح المسافات بين الأعمدة */
.dark .p-datatable .p-datatable-thead > tr > th:not(:last-child) {
  border-right: 1px solid rgb(55 65 81) !important;
}

/* إصلاح خلفية الجدول بالكامل */
.dark .p-datatable {
  background-color: rgb(31 41 55) !important;
  border-color: rgb(55 65 81) !important;
}

.dark .p-datatable .p-datatable-tbody > tr {
  background-color: rgb(31 41 55) !important;
  border-color: rgb(55 65 81) !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td {
  background-color: rgb(31 41 55) !important;
  border-color: rgb(55 65 81) !important;
  color: rgb(229 231 235) !important;
}

.dark .p-datatable .p-datatable-tbody > tr:hover > td {
  background-color: rgb(55 65 81) !important;
}

/* إصلاح الـ tr الرئيسي للجدول */
.dark .p-datatable .p-datatable-thead > tr {
  background-color: transparent !important;
  border-color: rgb(55 65 81) !important;
}

/* إصلاح إضافي للـ table wrapper */
.dark .p-datatable .p-datatable-wrapper {
  background-color: rgb(31 41 55) !important;
}

.dark .p-datatable .p-datatable-table {
  background-color: rgb(31 41 55) !important;
}

/* إصلاح الـ table header بشكل أكثر تحديداً */
.dark .p-datatable .p-datatable-header {
  background-color: transparent !important;
  border-bottom: 1px solid rgb(55 65 81) !important;
}

/* إصلاح شامل لجميع عناصر الجدول */
.dark .p-datatable * {
  border-color: rgb(55 65 81) !important;
}

.dark .p-datatable .p-datatable-thead * {
  background-color: transparent !important;
}

/* ضبط المسافات المناسبة في الجدول */
.dark .p-datatable .p-datatable-thead > tr > th {
  padding: 8px 12px !important;
  margin: 0 !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td {
  padding: 8px 12px !important;
  margin: 0 !important;
}

.dark .p-datatable {
  border-collapse: collapse !important;
}

.dark .p-datatable .p-datatable-table {
  border-collapse: collapse !important;
}

/* إزالة الحدود بين الأعمدة للحصول على مظهر متصل */
.dark .p-datatable .p-datatable-thead > tr > th + th {
  border-left: none !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td + td {
  border-left: none !important;
}

/* محاذاة النصوص في الأعمدة */
.dark .p-datatable .p-datatable-thead > tr > th {
  text-align: center !important;
  vertical-align: middle !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td {
  text-align: center !important;
  vertical-align: middle !important;
}

/* محاذاة خاصة للأعمدة النصية */
.dark .p-datatable .p-datatable-tbody > tr > td:first-child {
  text-align: left !important; /* الاسم في اليسار */
}

.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(2) {
  text-align: left !important; /* البريد في اليسار */
}

.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(3) {
  text-align: center !important; /* القسم في الوسط */
}

.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(4) {
  text-align: center !important; /* تفاصيل البطاقة في الوسط */
}

.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(5) {
  text-align: center !important; /* حالة الطباعة في الوسط */
}

.dark .p-datatable .p-datatable-tbody > tr > td:last-child {
  text-align: center !important; /* الإجراءات في الوسط */
}

/* ضبط عرض الأعمدة لضمان التوازي */
.dark .p-datatable .p-datatable-table {
  table-layout: auto !important;
  width: 100% !important;
}

/* ضبط عرض الأعمدة بناءً على الـ style المحدد */
.dark .p-datatable .p-datatable-thead > tr > th:nth-child(1) {
  width: 3em !important; /* Checkbox */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(2) {
  width: 275px !important; /* Template */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(3) {
  width: 200px !important; /* Name */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(4) {
  width: 130px !important; /* Type */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(5) {
  width: 130px !important; /* Position */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(6) {
  width: 130px !important; /* Department */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(7) {
  width: 260px !important; /* Card Details */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(8) {
  width: 260px !important; /* Card Assignment */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(9) {
  width: 220px !important; /* Status - زيادة العرض */
}

.dark .p-datatable .p-datatable-thead > tr > th:nth-child(10) {
  width: 220px !important; /* Actions */
}

/* ضبط محتوى الخلايا لضمان التوازي */
.dark .p-datatable .p-datatable-tbody > tr > td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* محاذاة خاصة لمحتوى Card Assignment وما بعدها */
.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(4) > div,
.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(5) > div,
.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(6) > div {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  min-height: 40px !important;
}

/* ضبط الأزرار في عمود الإجراءات */
.dark .p-datatable .p-datatable-tbody > tr > td:last-child > div {
  gap: 6px !important;
  flex-wrap: nowrap !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 4px !important;
}

/* ضبط أزرار الـ Actions */
.dark .p-datatable .p-datatable-tbody > tr > td:last-child .p-button {
  padding: 6px 8px !important;
  font-size: 11px !important;
  min-width: 32px !important;
  height: 28px !important;
  margin: 0 !important;
}

/* ضبط أيقونات الأزرار */
.dark .p-datatable .p-datatable-tbody > tr > td:last-child .p-button .p-button-icon {
  font-size: 11px !important;
}

/* ضبط أزرار الـ Actions بدون نص */
.dark .p-datatable .p-datatable-tbody > tr > td:last-child .p-button.p-button-icon-only {
  width: 28px !important;
  height: 28px !important;
  padding: 4px !important;
}

/* ضبط Badge في Card Assignment */
.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(4) .p-badge {
  white-space: nowrap !important;
  max-width: 100% !important;
  font-size: 11px !important;
}

/* ضبط Badge في Print Status */
.dark .p-datatable .p-datatable-tbody > tr > td:nth-child(5) .p-badge {
  white-space: nowrap !important;
  max-width: 100% !important;
  font-size: 11px !important;
}


/* Animations for Card Header */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.2); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.4), 0 0 30px rgba(255, 255, 255, 0.2); }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3), 0 0 10px rgba(59, 130, 246, 0.2);
    color: #ffffff;
  }
  50% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.6), 0 0 15px rgba(59, 130, 246, 0.4), 0 0 20px rgba(147, 197, 253, 0.3);
    color: #e0e7ff;
  }
}

.card-header-shimmer { animation: shimmer 2s infinite; }
.card-glow { animation: glow 3s ease-in-out infinite; }
.text-glow { animation: textGlow 3s ease-in-out infinite; }

.typing-effect {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid rgba(255, 255, 255, 0.8);
  width: 0;
  animation: typing 3s steps(20, end) forwards, blink-caret 0.75s step-end 3s, hide-cursor 0s 3s forwards;
  max-width: 100%;
}

@keyframes typing { from { width: 0; } to { width: 100%; } }
@keyframes blink-caret { 0%, 50% { border-color: rgba(255, 255, 255, 0.8); } 25%, 75% { border-color: transparent; } }
@keyframes hide-cursor { to { border-right: none; } }

.typing-effect-simple {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  width: 0;
  animation: typing 2s steps(15, end) forwards;
  max-width: 100%;
}

/* Modal Full Screen Styles for Light Mode */
.template-preview-dialog-fullscreen.p-dialog {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  z-index: 9999 !important;
}

.template-preview-dialog-fullscreen.p-dialog .p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.template-preview-dialog-fullscreen.p-dialog .p-dialog-content {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

.template-preview-dialog-fullscreen .p-dialog-header {
  width: 100% !important;
  padding: 1rem !important;
  background-color: #ffffff !important;
  border-bottom: 1px solid #e5e7eb !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
}

.template-preview-dialog-fullscreen .p-dialog-header .p-dialog-title {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 1.25rem !important;
}

.template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon {
  color: #6b7280 !important;
  font-size: 1.5rem !important;
}

.template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon:hover {
  color: #374151 !important;
  background-color: #f3f4f6 !important;
}

/* Dark mode overrides for modal */
.dark .template-preview-dialog-fullscreen .p-dialog-header {
  background-color: #1f2937 !important;
  border-bottom: 1px solid #374151 !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header .p-dialog-title {
  color: #f9fafb !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon {
  color: #9ca3af !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon:hover {
  color: #d1d5db !important;
  background-color: #374151 !important;
}

/* Force full screen for template modal - Override all conflicting styles */
.template-preview-dialog-fullscreen,
.template-preview-dialog-fullscreen.p-dialog,
.p-dialog.template-preview-dialog-fullscreen,
.p-dialog-mask .template-preview-dialog-fullscreen,
.p-dialog-mask .template-preview-dialog-fullscreen.p-dialog {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  z-index: 9999 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Override content area */
.template-preview-dialog-fullscreen .p-dialog-content,
.template-preview-dialog-fullscreen.p-dialog .p-dialog-content,
.p-dialog.template-preview-dialog-fullscreen .p-dialog-content {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  overflow: hidden !important;
  background: transparent !important;
  border: none !important;
}

/* Override header */
.template-preview-dialog-fullscreen .p-dialog-header,
.template-preview-dialog-fullscreen.p-dialog .p-dialog-header,
.p-dialog.template-preview-dialog-fullscreen .p-dialog-header {
  width: 100% !important;
  padding: 1rem !important;
  background-color: #ffffff !important;
  border-bottom: 1px solid #e5e7eb !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

/* Override mask */
.p-dialog-mask .template-preview-dialog-fullscreen,
.p-dialog-mask .template-preview-dialog-fullscreen.p-dialog {
  background: transparent !important;
  backdrop-filter: none !important;
}

/* Dark mode overrides for template modal */
.dark .template-preview-dialog-fullscreen .p-dialog-header,
.dark .template-preview-dialog-fullscreen.p-dialog .p-dialog-header,
.dark .p-dialog.template-preview-dialog-fullscreen .p-dialog-header {
  background-color: #1f2937 !important;
  border-bottom: 1px solid #374151 !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header .p-dialog-title,
.dark .template-preview-dialog-fullscreen.p-dialog .p-dialog-header .p-dialog-title,
.dark .p-dialog.template-preview-dialog-fullscreen .p-dialog-header .p-dialog-title {
  color: #f9fafb !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon,
.dark .template-preview-dialog-fullscreen.p-dialog .p-dialog-header .p-dialog-header-icon,
.dark .p-dialog.template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon {
  color: #9ca3af !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon:hover,
.dark .template-preview-dialog-fullscreen.p-dialog .p-dialog-header .p-dialog-header-icon:hover,
.dark .p-dialog.template-preview-dialog-fullscreen .p-dialog-header .p-dialog-header-icon:hover {
  color: #d1d5db !important;
  background-color: #374151 !important;
}

/* Exit Warning Modal Styles */
.exit-warning-modal {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  z-index: 9999 !important;
}

.exit-warning-modal .p-dialog-content {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Dark mode overrides for exit warning modal */
.dark .exit-warning-modal .p-dialog-content {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
/* Force full screen for exit warning modal - Override all conflicting styles */
.exit-warning-modal,
.exit-warning-modal.p-dialog,
.p-dialog.exit-warning-modal,
.p-dialog-mask .exit-warning-modal,
.p-dialog-mask .exit-warning-modal.p-dialog {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  z-index: 9999 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Override exit warning modal content */
.exit-warning-modal .p-dialog-content,
.exit-warning-modal.p-dialog .p-dialog-content,
.p-dialog.exit-warning-modal .p-dialog-content {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border: none !important;
}

/* Override exit warning modal mask */
.p-dialog-mask .exit-warning-modal,
.p-dialog-mask .exit-warning-modal.p-dialog {
  background: transparent !important;
  backdrop-filter: none !important;
}

/* Additional overrides to ensure full screen modals work */
.p-dialog-mask { background-color: rgba(0, 0, 0, 0.4) !important; }

/* Override any PrimeReact default styles that might conflict */
.p-dialog.template-preview-dialog-fullscreen,
.p-dialog.exit-warning-modal {
  transform: none !important;
  transition: none !important;
}

/* Ensure modal content takes full height */
.template-preview-dialog-fullscreen .template-preview-container,
.template-preview-dialog-fullscreen.p-dialog .template-preview-container,
.p-dialog.template-preview-dialog-fullscreen .template-preview-container {
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
}

/* Close button hover effects */
.template-preview-dialog-fullscreen .p-dialog-header button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Close button specific styling */
.template-preview-dialog-fullscreen .p-dialog-header button[title="Close Modal"]:hover {
  background-color: #fef2f2 !important;
  border-color: #fca5a5 !important;
  color: #dc2626 !important;
}

.dark .template-preview-dialog-fullscreen .p-dialog-header button[title="Close Modal"]:hover {
  background-color: rgba(127, 29, 29, 0.2) !important;
  border-color: #dc2626 !important;
  color: #fca5a5 !important;
}

/* Exit Warning Modal close button styling */
.exit-warning-modal button[title="Close Modal"]:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background-color: #fef2f2 !important;
  border-color: #fca5a5 !important;
  color: #dc2626 !important;
}

.dark .exit-warning-modal button[title="Close Modal"]:hover {
  background-color: rgba(127, 29, 29, 0.2) !important;
  border-color: #dc2626 !important;
  color: #fca5a5 !important;
}

